
<?php
session_start(); // Start the session to access data

// Configuration
$pricingApiUrl = "https://api.batscrm.com/ss/production/price"; // Production Pricing API URL
$authKey = "a68bd6e104f7497ea70b72c29472053d"; // Production Subscription Key
$partnerId = "4cc8d940-8256-4ea9-a536-d0421e6c8c65"; // Your Partner ID
$messageId = "f6becf0e-074b-4cdb-b00c-4f8f4104f334"; // Message ID for lead submission
$debugMode = false; // Toggle debug mode (true = enable, false = disable)

// Collect data from session
$origin_postal_code = $_SESSION['origin_postal_code'] ?? null;
$destination_postal_code = $_SESSION['destination_postal_code'] ?? null;
$origin_state = $_SESSION['origin_state'] ?? null;
$destination_state = $_SESSION['destination_state'] ?? null;
$vehicle_types = $_SESSION['vehicle_types'] ?? "Car"; // Default to "Car"
$transport_type = $_SESSION['transport_type'] ?? 1; // Default to Open
$vehicles_inop = $_SESSION['vehicle_inop'] ?? 0; // Default to Operable Vehicle
$vehicle_type = $_SESSION['vehicle_type'] ?? "Car"; // Default to "Car" if not set

// Ensure vehicle_type is one of the accepted valuesch
$valid_vehicle_types = ["Car", "SUV", "Pickup", "Van"];
if (!in_array($vehicle_type, $valid_vehicle_types)) {
    $vehicle_type = "Car"; // Default to "Car" if not valid
}

// If vehicle_types is not set or is empty, use vehicle_type
if (empty($vehicle_types)) {
    $vehicle_types = $vehicle_type;
}

// Ensure vehicle_types is one of the accepted values
$vehicle_types_array = explode(",", $vehicle_types);
$valid_vehicle_types_array = [];
foreach ($vehicle_types_array as $type) {
    $type = trim($type);
    if (in_array($type, $valid_vehicle_types)) {
        $valid_vehicle_types_array[] = $type;
    } else {
        $valid_vehicle_types_array[] = "Car"; // Default to "Car" if not valid
    }
}
$vehicle_types = implode(",", $valid_vehicle_types_array);

// Get customer info from session
$customer_info = $_SESSION['customer_info'] ?? [];

// Get transport information from session variables directly
$transport_from = isset($_SESSION['origin_city']) ? $_SESSION['origin_city'] . ', ' . $_SESSION['origin_state'] . ' ' . $_SESSION['origin_postal_code'] : '';
$transport_to = isset($_SESSION['destination_city']) ? $_SESSION['destination_city'] . ', ' . $_SESSION['destination_state'] . ' ' . $_SESSION['destination_postal_code'] : '';

// Fallback to customer_info if session variables are not set
if (empty($transport_from) && isset($customer_info['origin'])) {
    $transport_from = $customer_info['origin'];
}
if (empty($transport_to) && isset($customer_info['destination'])) {
    $transport_to = $customer_info['destination'];
}

// Get other customer information
$vehicle_brand = $_SESSION['vehicle_brand'] ?? $customer_info['vehicle_brand'] ?? '';
$vehicle_model = $_SESSION['vehicle_model'] ?? $customer_info['vehicle_model'] ?? '';
$vehicle_year = $_SESSION['vehicle_year'] ?? $customer_info['vehicle_year'] ?? '';
$email = $_SESSION['email'] ?? $customer_info['email'] ?? '';
$phone = $_SESSION['phone'] ?? $customer_info['phone'] ?? '';
$name = $_SESSION['name'] ?? $customer_info['name'] ?? '';
$transport_type_name = ($_SESSION['transport_type'] == 1) ? 'Open' : 'Enclosed';
if (empty($transport_type_name) && isset($customer_info['transport_type'])) {
    $transport_type_name = $customer_info['transport_type'];
}

// Check required parameters
if (!$origin_postal_code || !$destination_postal_code || !$origin_state || !$destination_state) {
    die("Required session data is missing. Please go back and fill out the form.");
}

// Prepare Pricing API Payload
$pricingPayload = [
    "partner_id" => $partnerId,
    "origin_state" => $origin_state,
    "origin_postal_code" => $origin_postal_code,
    "destination_state" => $destination_state,
    "destination_postal_code" => $destination_postal_code,
    "vehicle_types" => $vehicle_types, // Use vehicle_types instead of vehicle_type
    "transport_type" => (int)$transport_type, // 1 = Open, 2 = Enclosed
    "vehicles_inop" => (int)$vehicles_inop // 0 = Operable, 1 = Inoperable
];

// Ensure all values are properly set and not null
foreach ($pricingPayload as $key => $value) {
    if ($value === null) {
        if ($key === "vehicle_types") {
            $pricingPayload[$key] = "Car"; // Default to "Car" if null
        } elseif ($key === "transport_type") {
            $pricingPayload[$key] = 1; // Default to Open (1) if null
        } elseif ($key === "vehicles_inop") {
            $pricingPayload[$key] = 0; // Default to Operable (0) if null
        }
    }
}

// Function to Call the Pricing API
function fetchPricingData($url, $authKey, $data, $debugMode = false)
{
    $ch = curl_init($url);

    // Encode payload as JSON
    $jsonData = json_encode($data);

    // Configure cURL options
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json",
        "AuthKey: $authKey"
    ]);

    // Enable debug mode
    // if ($debugMode) {
    //     curl_setopt($ch, CURLOPT_VERBOSE, true);
    //     $verbose = fopen('php://temp', 'w+');
    //     curl_setopt($ch, CURLOPT_STDERR, $verbose);
    // }

    // Execute the request
    $response = curl_exec($ch);

    // Capture debug info if enabled
    // if ($debugMode) {
    //     if ($verbose) {
    //         rewind($verbose);
    //         $verboseLog = stream_get_contents($verbose);
    //         fclose($verbose);
    //         echo "<pre><strong>cURL Debug Info:</strong>\n" . htmlspecialchars($verboseLog) . "</pre>";
    //     }
    // }

    // Check for errors
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        return ["error" => $error];
    }

    // Close cURL
    curl_close($ch);

    // Decode JSON response
    return json_decode($response, true);
}

// Call the API and Get Pricing Data
$response = fetchPricingData($pricingApiUrl, $authKey, $pricingPayload, $debugMode);

// Get price from response
if (isset($response["body"]["price"])) {
    $shipping_price = $response["body"]["price"];
    $price_ref_id = $response["body"]["price_ref_id"] ?? '';
} elseif (isset($response["price"])) {
    $shipping_price = $response["price"];
    $price_ref_id = $response["price_ref_id"] ?? '';
} elseif (isset($response["total_tariff"])) {
    $shipping_price = $response["total_tariff"];
    $price_ref_id = $response["price_ref_id"] ?? '';
} else {
    $shipping_price = 0;
    $price_ref_id = '';
}

// Get Lead ID from session
$lead_id = $_SESSION['lead_id'] ?? 'Not available';
$api_error = $_SESSION['api_error'] ?? null;
$api_response_raw = $_SESSION['api_response_raw'] ?? null;

// Debug logging
$debug_info = [
    'timestamp' => date('Y-m-d H:i:s'),
    'lead_id_from_session' => $lead_id,
    'api_error' => $api_error,
    'api_response_raw' => $api_response_raw,
    'session_data' => $_SESSION
];

// Log debug info to file
file_put_contents('checkout_debug.log',
    "=== CHECKOUT DEBUG " . date('Y-m-d H:i:s') . " ===\n" .
    "Lead ID from session: " . var_export($lead_id, true) . "\n" .
    "API Error: " . var_export($api_error, true) . "\n" .
    "API Response Raw: " . var_export($api_response_raw, true) . "\n" .
    "Full Session Data: " . print_r($_SESSION, true) . "\n" .
    "==========================================\n\n",
    FILE_APPEND
);

// Clear the session data
unset($_SESSION['lead_id']);
unset($_SESSION['api_error']);
unset($_SESSION['api_response_raw']);

// Calculate transit time (example: 2-4 days)
$transit_days = "2 - 4 Days";

// // Debugging: Display Request and Response
if ($debugMode) {
    echo "<pre><strong>Request Payload:</strong> " . json_encode($pricingPayload, JSON_PRETTY_PRINT) . "</pre>";
    echo "<pre><strong>Response:</strong> " . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Redirect to thank you page with parameters
    $params = http_build_query([
        'transport_from' => $transport_from,
        'transport_to' => $transport_to,
        'vehicle_brand' => $vehicle_brand,
        'vehicle_model' => $vehicle_model,
        'total_tariff' => $shipping_price,
        'email' => $email,
        'phone' => $phone,
        'message_id' => $messageId,
        'lead_id' => $lead_id
    ]);

    header("Location: thank-you.php?" . $params);
    exit;
}
function findImagePath($baseName) {
    // Define fallbacks for specific icons
    $fallbacks = [
        'Door-To-Door.png' => ['door-to-door.svg', 'door-to-door-car-transport.svg', 'car-share.svg'],
        'insurance.png' => ['insurance.svg', 'shield.svg', 'car-share.svg'],
        'open-transport.png' => ['open-transport.svg', 'car-share.svg'],
        'no-hidden-fees.png' => ['no-hidden-fees.svg', 'dollar.svg', 'car-share.svg'],
        'customer-support.jpg' => ['customer-support.svg', 'support.svg', 'car-share.svg']
    ];

    // Standard paths to check
    $possiblePaths = [
        "public/images/{$baseName}",
        "/public/images/{$baseName}",
        "./public/images/{$baseName}",
        "../public/images/{$baseName}",
        "images/{$baseName}",
        "/images/{$baseName}",
        "./images/{$baseName}"
    ];

    // Add fallback paths if available for this basename
    if (isset($fallbacks[$baseName])) {
        foreach ($fallbacks[$baseName] as $fallback) {
            $possiblePaths[] = "public/images/{$fallback}";
            $possiblePaths[] = "/public/images/{$fallback}";
        }
    }

    // Check which path exists
    foreach ($possiblePaths as $path) {
        if (file_exists($_SERVER['DOCUMENT_ROOT'] . '/' . ltrim($path, '/'))) {
            return $path;
        }
    }

    // Based on the file excerpts, we know these files exist
    if (strpos($baseName, 'Door-To-Door') !== false) {
        return "public/images/Book2.png"; // From door-to-door-car-transport.php
    } else if (strpos($baseName, 'open-transport') !== false) {
        return "public/images/car-share.svg"; // From the retrieval results
    } else if (strpos($baseName, 'customer-support') !== false) {
        return "public/images/CarKey1.png"; // From how-to-ship-a-car.php
    }

    // Default fallback to Font Awesome icons
    return "";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Summary - Safe Car Hauler</title>

    <link rel="stylesheet" type="text/css" href="public/css/index.css" />
    <link rel="stylesheet" type="text/css" href="public/css/styles.css" />
    <link rel="stylesheet" type="text/css" href="public/css/components.css" />
    <link rel="stylesheet" type="text/css" href="public/css/main.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Nohemi', sans-serif;
            background-color: #f5f5f7;
            margin: 0;
            padding: 0;
            padding-top: 101px !important;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding-top: 100px;
            padding-left: 15px;
            padding-right: 15px;
        }
        .breadcrumb {
            margin-bottom: 20px;
            color: #666;
            font-size: 14px;
        }
        .breadcrumb a {
            color: #666;
            text-decoration: none;
        }
        .order-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #222a31;
        }
        .route-info {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            background-color: #FFF;
            border-radius: 50px;
            padding: 5px;
            width: fit-content;
            max-width: 100%;
        }
        .location {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            white-space: nowrap;
        }
        .location-icon {
            color: #cda565;
            margin-right: 8px;
        }
        .arrow {
            margin: 0 15px;
            color: #999;
            font-size: 18px;
        }
        .content-wrapper {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .left-column {
            flex: 2;
            min-width: 300px;
        }
        .right-column {
            flex: 1;
            min-width: 418px;
        }
        .card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #222a31;
        }
        .shipment-summary {
            padding: 20px;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        .summary-row:last-child {
            border-bottom: none;
        }
        .summary-label {
            color: #666;
        }
        .summary-value {
            font-weight: 500;
            color: #222a31;
        }
        .features {
            display: flex;
            justify-content: space-between;
            padding: 20px;
            text-align: center;
        }
        .features-card {
            padding: 10px 0;
        }
        .feature {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 25%;
        }
        .feature-icon-container {
            width: 60px;
            height: 60px;
            background-color: #f8f8f8;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #cda565;
            font-size: 24px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .feature-icon {
            width: 50px;
            height: 50px;
            margin-bottom: 10px;
        }
        .feature-text {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }
        .support-card {
            padding: 20px;
            display: flex;
            align-items: center;
        }
        .support-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 20px;
            flex-shrink: 0;
        }
        .support-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .support-text h3 {
            font-size: 18px;
            color: #222a31;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .support-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background-color: #cda565;
            color: white;
            border-radius: 50%;
            margin-right: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        .support-text p {
            color: #666;
            margin-bottom: 5px;
            font-size: 14px;
            line-height: 1.4;
        }
        .phone-number {
            color: #cda565;
            font-weight: 600;
            text-decoration: none;
        }
        .map-container {
            height: 300px;
            width: 100%;
        }
        .map-card {
            padding: 0;
            overflow: hidden;
        }
        .price-card {
            padding: 20px;
            background-color: #fff;
        }
        .price-header {
            background-color: #fff9e6;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .price-title {
            font-size: 20px;
            font-weight: 700;
            color: #222a31;
            margin-bottom: 5px;
        }
        .price-subtitle {
            color: #666;
            font-size: 14px;
        }
        .price-title-mobile{
            font-size: 22px !important;
            font-weight: 600 !important;
        }
        .subtotal-row-mobile > div:last-child {
            font-weight: 500 !important;
        }
        .price-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .price-row:last-of-type {
            border-bottom: 2px solid #eee;
        }
        .price-title-mobile{
            padding-top:5px !important;
        }
        .price-subtitle-mobile{
            padding: 10px 0 !important;
        }
        .price-label {
            color: #666;
        }
        .price-value {
            font-weight: 500;
        }
         /* Removed default location i styling to use inline styles */
        .subtotal-row {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            font-size: 20px;
            font-weight: 700;
        }
        .subtotal-value {
            color: #cda565;
        }
        .book-button {
            background-color: #cda565;
            color: white;
            border: none;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .book-button:hover {
            background-color: #b58544;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                flex-direction: column;
            }
            .features {
                flex-wrap: wrap;
            }
            .feature {
                width: 50%;
                margin-bottom: 20px;
            }
        }

        /* Tablet Responsive Design */
        @media (max-width: 880px) {
            .container {
                max-width: 95%;
                padding-top: 70px;
            }

            .content-wrapper {
                flex-direction: column;
            }

            /* Hide order-title on tablet */
            .order-title {
                display: none;
            }

            /* Show price header text above route-info without background */
            .price-header-tablet {
                display: block !important;
                background: none !important;
                padding: 0 !important;
                margin-bottom: 15px !important;
            }

            /* Show book button below route-info - only button, no prices */
            .tablet-book-section {
                display: block !important;
            }

            .book-button-tablet {
                background-color: #cda565 !important;
                color: white !important;
                border: none !important;
                padding: 15px !important;
                font-size: 18px !important;
                font-weight: 600 !important;
                border-radius: 10px !important;
                cursor: pointer !important;
                /* width: 100% !important; */
                width: 200px !important;
                margin-top: 20px !important;
                transition: background-color 0.3s !important;
            }

            .book-button-tablet:hover {
                background-color: #b58544 !important;
            }

            /* Hide the price details but keep the map visible on tablet */
            .price-header-mobile {
                display: none !important;
            }

            .price-content-mobile > form {
                display: none !important;
            }

            /* Keep the map visible */
            .map-container {
                display: block !important;
            }

            /* Reorder columns to show map first */
            .right-column {
                order: -1;
                width: 100%;
                margin-bottom: 15px;
                margin-top: 20px;
            }

            .left-column {
                width: 100%;
            }

            .map-container {
                height: 200px;
                border-radius: 10px;
            }

            .card {
                margin-bottom: 15px;
                max-width: 100% !important;
                margin-left: 0px !important;
                width: 100% !important;
            }

            .breadcrumb {
                font-size: 12px;
            }

            .summary-row, .price-row {
                padding: 8px 0;
                font-size: 14px;
            }

            .subtotal-row {
                font-size: 18px;
            }
            .route-info{
                margin-bottom: 0px;
            }
        }
        /* Mobile Responsive Design */
        @media (max-width: 480px) {
            .container {
                max-width: 100%;
                padding-top: 100px;
                margin: 0;
                padding-bottom: 0px;
            }
            .summary-mb-label-pl{
                font-size: 14px !important;
            }
            .mb-line-sr{
                display: none;
            }
            .breadcrumb {
                font-size: 12px;
                margin-bottom: 12px;
                padding:10px !important;
            }
            .price-header-tablet{
                padding: 10px !important;
                margin-bottom: 0px !important;
            }
            .order-title {
                font-size: 20px;
                margin-bottom: 10px;
                line-height: 1.2;
            }
            .tablet-book-section{
                padding: 10px !important;
            }
            .route-info {
                margin-bottom: 0px !important;
                margin-left: 10px;
                display: none;
            }
            .price-subtitle-tablet{
                font-weight: 300;
                margin-bottom: 0 !important;
            }
            
            .location {
                font-size: 10px;
                padding: 6px 12px;
                font-weight: 400;
            }

            .content-wrapper {
                flex-direction: column;
                gap: 15px;
                padding:10px !important;
            }

            /* Hide map on mobile */
            .map-container, #map {
                display: none !important;
            }

            /* Reorder content */
            .right-column {
                order: -1;
                margin-bottom: 5px;
                min-width: 200px;
            }

            .left-column, .right-column {
                width: 100%;
            }

            .card {
                margin-bottom: 15px;
                border-radius: 10px;
            }

            .price-card {
                padding: 18px;
            }

            .price-header {
                padding: 15px;
                margin-bottom: 15px;
            }

            .price-title {
                font-size: 18px;
                margin-bottom: 8px;
            }

            .price-subtitle {
                font-size: 14px;
            }

            .price-row {
                padding: 10px 0;
                font-size: 15px;
            }

            .price-label, .summary-label {
                font-size: 15px;
            }
            .summary-mb-label{
                font-size: 13px !important;
            }
            .price-value, .summary-value {
                font-size: 10px;
                font-weight: 600;
            }

            .subtotal-row {
                padding: 15px 0;
                font-size: 18px;
            }

            .book-button {
                padding: 14px;
                font-size: 16px;
                margin-top: 12px;
                font-weight: 700;
            }
            .book-button-tablet{
                width: 100% !important;
                margin-top: 0px !important;
            }
            .shipment-summary {
                padding: 18px;
            }
            .card-mb-header{
                background-color: #FFF6E6 !important;
            }
            .card-title {
                font-size: 18px;
                margin-bottom: 15px;
                font-weight: 700;
            }
            .card-mb-title{
                font-size: 18px !important;
              
            }
            .summary-row {
                font-size: 15px;
                padding: 10px 0;
            }

            .features {
                display: grid !important;
                grid-template-columns: repeat(2, 1fr);
                gap: 0px !important;
                max-width: 800px;
                margin: 0 auto;
                padding: 5px;

            }

            .feature {
                width: 100%;
                padding: 10px;
                text-align: center;
            }
            .mb-summ-xs{
                font-size: 13px !important;
            }
            .feature-text {
                font-size: 13px;
                line-height: 1.3;
                margin-top: 8px;
            }

            .feature-icon {
                width: 51px !important;
                height: 51px !important;
                margin-bottom: 8px;
            }

            .support-text h3 {
                font-size: 18px;
                margin-bottom: 8px;
            }

            .support-text p {
                font-size: 14px;
                line-height: 1.5;
            }

            .phone-number {
                font-size: 16px;
                font-weight: 700;
            }

            /* Target the new classes for price card */
            .price-header-mobile {
                padding: 10px !important;
                margin-top: 0px !important;
            }
            .support-mb-title{
                font-size: 18px !important;
            }
            .support-mb-text{
                font-size: 14px !important;
            }
            .support-mb-icon{
                width: 40px !important;
                /* height: 40px !important; */
            }
            .price-title-mobile {
                font-size: 16px !important;
                margin-bottom: 2px !important;
            }

            .price-subtitle-mobile {
                font-size: 12px !important;
            }

            .price-content-mobile {
                padding: 12px !important;
            }
            .image-mb-container{
                display: none !important;
            }
            .card{
                height: auto !important;
            }
            .price-row-mobile {
                padding: 8px 0 !important;
            }

            .price-row-mobile div:first-child {
                font-size: 14px !important;
            }

            .price-row-mobile div:last-child {
                font-size: 14px !important;
            }

            .subtotal-row-mobile {
                padding: 12px 0 !important;
                font-size: 16px !important;
            }

            .subtotal-row-mobile div:last-child {
                font-size: 16px !important;
            }

            .book-button-mobile {
                padding: 12px !important;
                font-size: 15px !important;
                margin-top: 8px !important;
            }
            
        }
        @media (max-width: 380px){
            .location{
                /* padding:0px !important; */
                font-size: 9px;
            }
            .arrow{
                margin:0 !important;
            }
        }
        @media (max-width: 340px){
            .left-column {
                min-width: 280px !important;
            }
            .route-info{
                margin-left: 0px !important;
            }
        }
    </style>
    
    <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NGJGCPBZ');</script>
<!-- End Google Tag Manager -->

</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="container">
        <div class="breadcrumb" style="display: flex; align-items: center;">
            <a href="index.php">Home</a> <span style="margin: 0 5px;">\</span> <span style="font-weight: bold; color:#000;">Quote Summary</span>
        </div>

        <h1 class="order-title">Order #<?php echo htmlspecialchars($lead_id); ?></h1>

        <?php if ($debugMode || isset($_GET['debug'])): ?>
        <div style="background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; font-family: monospace; font-size: 12px;">
            <h3>🐛 DEBUG INFO:</h3>
            <p><strong>Lead ID:</strong> <?php echo htmlspecialchars($lead_id); ?></p>
            <p><strong>API Error:</strong> <?php echo htmlspecialchars($api_error ?? 'None'); ?></p>
            <p><strong>API Response:</strong> <?php echo htmlspecialchars($api_response_raw ?? 'None'); ?></p>
            <p><strong>Session Origin:</strong> <?php echo htmlspecialchars($_SESSION['origin_city'] ?? 'Not set'); ?></p>
            <p><strong>Session Destination:</strong> <?php echo htmlspecialchars($_SESSION['destination_city'] ?? 'Not set'); ?></p>
            <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
        <?php endif; ?>

        <!-- Price header for tablet - appears above route-info -->
        <div class="price-header-tablet" style="display: none;">
            <h3 class="price-title-tablet">Your Car Shipping Price Is Ready</h3>
            <p class="price-subtitle-tablet">We've Matched Your Route And Vehicle With Our Most Competitive Rate.</p>
        </div>

        <div class="route-info">
            <div class="location">
               <i class="fas fa-map-marker-alt" style="color: #e2caa4; font-size: 18px; margin-right: 10px;"></i>
                <?php echo htmlspecialchars($_SESSION['origin_city'] . ', ' . $_SESSION['origin_state'] . ' ' . $_SESSION['origin_postal_code']); ?>
            </div>
            <span class="arrow">→</span>
            <div class="location">
                <img src="public/images/Vector-2.svg" style="color: #E2B16A; font-size: 18px; margin-right: 10px;"></i>
                <?php echo htmlspecialchars($_SESSION['destination_city'] . ', ' . $_SESSION['destination_state'] . ' ' . $_SESSION['destination_postal_code']); ?>
            </div>
        </div>

        <!-- Book button for tablet - appears below route-info -->
        <div style="display: none;" class="tablet-book-section">
            <form method="post">
                <button type="submit" class="book-button-tablet">Book The Shipment</button>
            </form>
        </div>

        <div class="content-wrapper">
            <div class="left-column">
                <div class="card" style="    
    border-radius: 10px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-bottom: 20px;">
                    <div class="card-mb-header" style="background-color: #F1F5F9; padding: 20px;">
                        <h2 class="card-mb-title" style="margin: 0; font-size: 24px; font-weight: 600; color: #222a31;">Shipment Summary:</h2>
                    </div>

                    <div style="background-color: white; padding: 0;">
                    
                        <div class="summary-row" style="display: flex; justify-content: space-between; padding: 20px; border-bottom: 1px solid #eee;">
                            <div class="mb-summ-xs" style="color: #666; font-size: 18px;">Pickup From</div>
                            <div class="summary-mb-label" style="font-weight: 300; color: #222a31; font-size: 18px;"><?php echo htmlspecialchars($_SESSION['origin_city'] . ', ' . $_SESSION['origin_state'] . ' ' . $_SESSION['origin_postal_code']); ?></div>
                        </div>

                        <div class="summary-row" style="display: flex; justify-content: space-between; padding: 20px; border-bottom: 1px solid #eee;">
                            <div class="mb-summ-xs" style="color: #666; font-size: 18px;">Deliver To</div>
                            <div class="summary-mb-label" style="font-weight: 300; color: #222a31; font-size: 18px;"><?php echo htmlspecialchars($_SESSION['destination_city'] . ', ' . $_SESSION['destination_state'] . ' ' . $_SESSION['destination_postal_code']); ?></div>
                        </div>

                        <div class="summary-row" style="display: flex; justify-content: space-between; padding: 20px; border-bottom: 1px solid #eee;">
                            <div class="mb-summ-xs" style="color: #666; font-size: 18px;">Vehicle</div>
                            <div class="summary-mb-label" style="font-weight: 300; color: #222a31; font-size: 18px;"><?php echo htmlspecialchars($_SESSION['vehicle_year'] . ' ' . $_SESSION['vehicle_brand'] . ' ' . $_SESSION['vehicle_model']); ?></div>
                        </div>

                        <div class="summary-row" style="display: flex; justify-content: space-between; padding: 20px; border-bottom: 1px solid #eee;">
                            <div class="mb-summ-xs" style="color: #666; font-size: 18px;">Transport Type</div>
                            <div class="summary-mb-label-pl" style="font-weight: 500; color: #222a31; font-size: 18px;"><?php echo ($_SESSION['transport_type'] == 1) ? 'Open Carrier' : 'Enclosed Carrier'; ?></div>
                        </div>

                        <div class="summary-row" style="display: flex; justify-content: space-between; padding: 20px; border-bottom: 1px solid #eee;">
                            <div class="mb-summ-xs" style="color: #666; font-size: 18px;">Lead ID</div>
                            <div class="summary-mb-label" style="font-weight: 600; color: #cda565; font-size: 18px;"><?php echo htmlspecialchars($lead_id); ?></div>
                        </div>

                    </div>
                </div>

                <div class="card features-card" style="margin-bottom: 20px; border-radius: 16px; background-color: white; ">
                    <div class="features" style="display: flex; justify-content: space-between; gap: 40px;">
                        <div class="feature" style="flex: 1; text-align: center; position: relative;">
                            <img src="public/icons/Door-to-Door-Dark.png" alt="Door-To-Door" class="feature-icon" style="margin-bottom: 10px; width: 60.9473px; height: 60.9473px;">
                            <div style="font-size: 16px; font-weight: 300; color: #222a31; line-height: 150%;">Door-To-Door<br>Service</div>
                            <div class="mb-line-sr" style="position: absolute; top: 50%; right: -20px; height: 48px; width: 1px; background-color: #eee; transform: translateY(-50%);"></div>
                        </div>
                        <div class="feature" style="flex: 1; text-align: center; position: relative;">
                            <img src="public/icons/Insurance-Dark.png" alt="Insurance" class="feature-icon" style="margin-bottom: 10px; width: 60.9473px; height: 60.9473px;">
                            <div style="font-size: 16px; font-weight: 300; color: #222a31; line-height: 150%;">Full Insurance<br>Coverage</div>
                            <div class="mb-line-sr" style="position: absolute; top: 50%; right: -20px; height: 48px; width: 1px; background-color: #eee; transform: translateY(-50%);"></div>
                        </div>
                        <div class="feature" style="flex: 1; text-align: center; position: relative;">
                            <img src="public/icons/Inspections-Dark.png" alt="Open Transport" class="feature-icon" style="margin-bottom: 10px; width: 60.9473px; height: 60.9473px;">
                            <div style="font-size: 16px; font-weight: 300; color: #222a31; line-height: 150%;">Open Transport<br>Option</div>
                            <div class="mb-line-sr" style="position: absolute; top: 50%; right: -20px; height: 48px; width: 1px; background-color: #eee; transform: translateY(-50%);"></div>
                        </div>
                        <div class="feature" style="flex: 1; text-align: center;">
                            <img src="public/icons/No-Payment-Dark.png" alt="No Hidden Fees" class="feature-icon" style="margin-bottom: 10px; width: 60.9473px; height: 60.9473px;">
                            <div style="font-size: 16px; font-weight: 300; color: #222a31; line-height: 150%;">No Hidden<br>Fees</div>
                        </div>
                    </div>
                </div>

                <div class="card" style="padding: 20px 18px; overflow: hidden; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; width: auto; height: 190px; border-bottom: 1px solid #ccc; gap: 24px;">
                    <div style="background-color: #fff; padding: 0; display: flex; flex-direction: column; height: 100%;">
                        <div style="padding: 20px; flex: 1;">
                            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                <span class="support-mb-icon" style="display: inline-flex; align-items: center; justify-content: center; width: 24px; height: 24px; background-color: #cda565; color: white; border-radius: 50%; margin-right: 10px; font-size: 14px; font-weight: bold;">?</span>
                                <h3 class="support-mb-title" style="font-size: 22px; color: #333; margin: 0; font-weight: 700;">Still Have Questions About Your Car Shipment?</h3>
                            </div>

                            <p class="support-mb-text" style="color: #666; font-size: 18px; line-height: 1.5; margin-top: 15px;">
                                Call <a href="tel:+18778788008" style="color: #cda565; font-weight: 600; text-decoration: none; display:inline;">(*************</a> To Speak Directly With A Car Shipping Specialist. We're Here To Walk You Through The Process And Answer Any Questions You May Have.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="right-column">
                <div class="card" style="
                max-width: 434px;
                margin-left: 27px;
                padding: 0; border-radius: 20px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div id="map" class="map-container" style="height: 300px; width: 100%; border-radius: 20px 20px 0 0;"></div>

                    <div class="price-header-mobile" style="background-color: #fff9e6; padding: 15px; border-radius: 20px 20px 0 0; margin-top: -20px; position: relative; z-index: 1;">
                        <h3 class="price-title-mobile" style="font-size: 24px; font-weight: 700; color: #222a31; margin: 0 0 5px 0;">Your Car Shipping Price Is Ready</h3>
                        <p class="price-subtitle-mobile" style="color: #666; font-size: 16px; margin: 0;">We've Matched Your Route And Vehicle With Our Most Competitive Rate.</p>
                    </div>

                    <div class="price-content-mobile" style="padding: 20px; background-color: white;">
                        <div class="price-row-mobile" style="display: flex; justify-content: space-between; padding: 15px 0; border-bottom: 1px solid #eee;">
                            <div style="color: #666; font-size: 18px;">Shipping</div>
                            <div style="font-weight: 500; font-size: 18px;">$<?php echo number_format($shipping_price + 70, 2); ?></div>
                        </div>

                        <div class="price-row-mobile" style="display: flex; justify-content: space-between; padding: 15px 0; border-bottom: 1px solid #eee;">
                            <div style="color: #666; font-size: 18px;">Discounts</div>
                            <div style="font-weight: 500; font-size: 18px;">-$<?php echo number_format(70, 2); ?></div>
                        </div>

                        <div class="subtotal-row-mobile" style="display: flex; justify-content: space-between; padding: 20px 0; font-size: 24px; font-weight: 700;">
                            <div>Subtotal</div>
                            <div style="color: #cda565;">$<?php echo number_format($shipping_price, 2); ?></div>
                        </div>

                        <form method="post">
                            <button type="submit" class="book-button-mobile" style="background-color: #cda565; color: white; border: none; padding: 15px; font-size: 18px; font-weight: 600; border-radius: 10px; cursor: pointer; width: 100%; margin-top: 10px; transition: background-color 0.3s;">Book The Shipment</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function initMap() {
            const originState = "<?php echo $_SESSION['origin_state']; ?>";
            const destinationState = "<?php echo $_SESSION['destination_state']; ?>";
            const originPostalCode = "<?php echo $_SESSION['origin_postal_code']; ?>";
            const destinationPostalCode = "<?php echo $_SESSION['destination_postal_code']; ?>";
            const originAddress = "<?php echo addslashes($_SESSION['origin_city'] . ', ' . $_SESSION['origin_state'] . ' ' . $_SESSION['origin_postal_code']); ?>";
            const destinationAddress = "<?php echo addslashes($_SESSION['destination_city'] . ', ' . $_SESSION['destination_state'] . ' ' . $_SESSION['destination_postal_code']); ?>";

            const map = new google.maps.Map(document.getElementById("map"), {
                zoom: 4,
                center: { lat: 39.8283, lng: -98.5795 }, // Center of US
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                mapTypeControl: false,
                fullscreenControl: false,
                streetViewControl: false,
                zoomControlOptions: {
                    position: google.maps.ControlPosition.LEFT_TOP
                },
                styles: [
                    {
                        "featureType": "water",
                        "elementType": "geometry",
                        "stylers": [
                            { "color": "#a4ddf5" }
                        ]
                    },
                    {
                        "featureType": "landscape",
                        "elementType": "geometry",
                        "stylers": [
                            { "color": "#e8f0e5" }
                        ]
                    },
                    {
                        "featureType": "road",
                        "elementType": "geometry",
                        "stylers": [
                            { "color": "#ffffff" }
                        ]
                    }
                ]
            });

            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                map: map,
                suppressMarkers: false,
                polylineOptions: {
                    strokeColor: "#4285F4",
                    strokeWeight: 5
                }
            });

            // Use the full addresses if available, otherwise use postal codes
            const originLocation = originAddress || (originPostalCode + ", " + originState);
            const destinationLocation = destinationAddress || (destinationPostalCode + ", " + destinationState);

            directionsService.route(
                {
                    origin: originLocation,
                    destination: destinationLocation,
                    travelMode: google.maps.TravelMode.DRIVING
                },
                (response, status) => {
                    if (status === "OK") {
                        directionsRenderer.setDirections(response);
                    } else {
                        console.error("Directions request failed due to " + status);
                    }
                }
            );
        }
    </script>
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDPqmvF0Uf9aR1N1hQZVSUyibk__vkaegk&callback=initMap"></script>

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NGJGCPBZ"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <?php include 'includes/footer.php'; ?>
</body>
</html>