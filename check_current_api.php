<?php
// Check what the current API is returning

echo "<h1>🔍 Current BATS CRM API Response Check</h1>";

// From your debug log, we know the current response is:
$current_response = "OK, Lead : ID no longer provided";

echo "<h2>📋 Current API Response Analysis:</h2>";
echo "<p><strong>Current Response:</strong> <code>" . htmlspecialchars($current_response) . "</code></p>";

echo "<h2>🧪 Testing Lead ID Extraction Methods:</h2>";

// Test current extraction methods
$lead_id = null;

echo "<h3>Method 1: JSON Decode</h3>";
$responseData = json_decode($current_response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "<p>✅ JSON decode successful</p>";
    $lead_id = $responseData['lead_id'] ?? 
              $responseData['data']['lead_id'] ?? 
              $responseData['id'] ?? null;
    if ($lead_id) {
        echo "<p>✅ Found lead ID: $lead_id</p>";
    } else {
        echo "<p>❌ No lead ID in JSON</p>";
    }
} else {
    echo "<p>❌ Not valid JSON: " . json_last_error_msg() . "</p>";
}

echo "<h3>Method 2: Regex - Old Format</h3>";
if (preg_match('/OK, Lead :(\d+)/', $current_response, $matches)) {
    $lead_id = $matches[1];
    echo "<p>✅ Found with old regex: $lead_id</p>";
} else {
    echo "<p>❌ Old regex pattern doesn't match</p>";
}

echo "<h3>Method 3: Alternative Regex Patterns</h3>";
$patterns = [
    '/Lead[: ]+(\d+)/' => 'Lead followed by number',
    '/ID[: ]+(\d+)/' => 'ID followed by number', 
    '/(\d+)/' => 'Any number in response',
    '/Lead[: ]+([A-Z0-9-]+)/' => 'Lead followed by alphanumeric',
    '/ID[: ]+([A-Z0-9-]+)/' => 'ID followed by alphanumeric'
];

foreach ($patterns as $pattern => $description) {
    if (preg_match($pattern, $current_response, $matches)) {
        echo "<p>✅ Pattern '$description' matched: <strong>{$matches[1]}</strong></p>";
    } else {
        echo "<p>❌ Pattern '$description' no match</p>";
    }
}

echo "<hr>";

echo "<h2>💡 Possible Solutions:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7;'>";
echo "<h3>Option 1: Generate Our Own Lead ID</h3>";
echo "<p>Since BATS CRM API response is now 'OK, Lead : ID no longer provided', we can:</p>";
echo "<ul>";
echo "<li>Generate our own unique lead ID format</li>";
echo "<li>Use timestamp + random string</li>";
echo "<li>Store it in our database for tracking</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; border: 1px solid #bee5eb; margin-top: 10px;'>";
echo "<h3>Option 2: Check API Documentation</h3>";
echo "<p>The API might have changed and now returns lead ID in a different format or requires different parameters.</p>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb; margin-top: 10px;'>";
echo "<h3>Option 3: Use Success Response as Confirmation</h3>";
echo "<p>Since we get 'OK, Lead : ID no longer provided', we know the lead was submitted successfully. We can generate our own tracking ID.</p>";
echo "</div>";

echo "<hr>";

echo "<h2>🚀 Recommended Implementation:</h2>";
echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;'>";
echo htmlspecialchars('
// If API returns success but no lead ID, generate our own
if ($httpCode === 200 && strpos($result, "OK, Lead") !== false) {
    // Generate unique lead ID
    $lead_id = date("Ymd") . "-" . strtoupper(substr(uniqid(), -6));
    // Example: 20250718-A1B2C3
    
    // Log the successful submission
    error_log("Lead submitted successfully. Generated ID: " . $lead_id);
} else {
    // Handle error cases
    $lead_id = "ERR-" . uniqid();
}
');
echo "</pre>";

echo "<p><em>Analysis completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
