<?php
echo "<h1>🧪 API Response Test</h1>";

// Test with production key
$authKey = "a68bd6e104f7497ea70b72c29472053d";

$testData = [
    "AuthKey" => $authKey,
    "first_name" => "Test",
    "last_name" => "User",
    "email" => "<EMAIL>",
    "phone" => "1234567890",
    "comment_from_shipper" => "Test lead",
    "origin_city" => "New York",
    "origin_state" => "NY",
    "origin_postal_code" => "10001",
    "origin_country" => "US",
    "destination_city" => "Los Angeles",
    "destination_state" => "CA",
    "destination_postal_code" => "90001",
    "destination_country" => "US",
    "ship_date" => date('m/d/Y'),
    "transport_type" => 1,
    "Vehicles" => [[
        "vehicle_inop" => 0,
        "vehicle_make" => "Toyota",
        "vehicle_model" => "Camry",
        "vehicle_model_year" => 2020,
        "vehicle_type" => "Car"
    ]]
];

echo "<h2>📤 Request Data:</h2>";
echo "<pre style='background: #f0f8ff; padding: 10px; border-radius: 5px;'>";
echo json_encode($testData, JSON_PRETTY_PRINT);
echo "</pre>";

// Send API request
$ch = curl_init("https://api.batscrm.com/leads");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($testData))
]);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "<h2>📥 API Response:</h2>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
if ($curlError) {
    echo "<p><strong>cURL Error:</strong> $curlError</p>";
}
echo "<p><strong>Raw Response:</strong></p>";
echo "<pre style='background: #fff0f0; padding: 10px; border-radius: 5px; border: 1px solid #ffcccc;'>";
echo htmlspecialchars($result);
echo "</pre>";

// Test lead ID extraction
echo "<h2>🔍 Lead ID Extraction Test:</h2>";
$lead_id = null;

// First try JSON decode
$responseData = json_decode($result, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "<p>✅ JSON decode successful</p>";
    echo "<pre>";
    print_r($responseData);
    echo "</pre>";
    
    $lead_id = $responseData['lead_id'] ?? 
              $responseData['data']['lead_id'] ?? 
              $responseData['id'] ?? 
              $responseData['leadId'] ?? 
              $responseData['Lead_ID'] ?? null;
    
    if ($lead_id) {
        echo "<p>✅ Found lead ID in JSON: <strong>$lead_id</strong></p>";
    } else {
        echo "<p>❌ No lead ID found in JSON response</p>";
    }
} else {
    echo "<p>❌ JSON decode failed: " . json_last_error_msg() . "</p>";
}

// Try text parsing
if (!$lead_id) {
    echo "<p>🔍 Trying text parsing...</p>";
    
    // Check for "OK, Lead :21046214" format
    if (preg_match('/OK, Lead :(\d+)/', $result, $matches)) {
        $lead_id = $matches[1];
        echo "<p>✅ Found lead ID with regex 1: <strong>$lead_id</strong></p>";
    }
    // Check for other possible text formats
    elseif (preg_match('/Lead(?: ID)?[: ]*(\d+)/i', $result, $matches)) {
        $lead_id = $matches[1];
        echo "<p>✅ Found lead ID with regex 2: <strong>$lead_id</strong></p>";
    }
    // Check for new format possibilities
    elseif (preg_match('/ID[: ]*(\d+)/i', $result, $matches)) {
        $lead_id = $matches[1];
        echo "<p>✅ Found lead ID with regex 3: <strong>$lead_id</strong></p>";
    }
    else {
        echo "<p>❌ No lead ID found with text parsing</p>";
    }
}

if ($lead_id) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb; margin: 20px 0;'>";
    echo "<h3>✅ SUCCESS: Lead ID extracted: $lead_id</h3>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb; margin: 20px 0;'>";
    echo "<h3>❌ FAILED: Could not extract lead ID</h3>";
    echo "<p>Response was: " . htmlspecialchars($result) . "</p>";
    echo "</div>";
}

// Test with different API endpoints
echo "<hr>";
echo "<h2>🔄 Testing Alternative API Endpoints:</h2>";

$endpoints = [
    "https://api.batscrm.com/leads",
    "https://api.batscrm.com/v1/leads", 
    "https://api.batscrm.com/v2/leads"
];

foreach ($endpoints as $endpoint) {
    echo "<h3>Testing: $endpoint</h3>";
    
    $ch = curl_init($endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($testData))
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p>HTTP: $httpCode | Response: " . htmlspecialchars(substr($result, 0, 100)) . "...</p>";
}

echo "<hr>";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
