<?php
session_start();

echo "<h1>🧪 Test New Lead ID Logic</h1>";

// Simulate different API responses
$testResponses = [
    'OK, Lead :21619062' => 'Old format with lead ID',
    'OK, Lead : ID no longer provided' => 'New format without lead ID',
    'OK, Lead created successfully' => 'Success without ID',
    'Error: Invalid data' => 'Error response',
    '{"lead_id": "12345"}' => 'JSON with lead ID',
    '{"success": true}' => 'JSON without lead ID'
];

echo "<h2>🔍 Testing Lead ID Extraction Logic:</h2>";

foreach ($testResponses as $response => $description) {
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>Test: $description</h3>";
    echo "<p><strong>Response:</strong> <code>" . htmlspecialchars($response) . "</code></p>";
    
    // Test the extraction logic
    $lead_id = null;
    
    // First try JSON decode
    $responseData = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        $lead_id = $responseData['lead_id'] ?? 
                  $responseData['data']['lead_id'] ?? 
                  $responseData['id'] ?? null;
    }
    
    // If JSON decode failed or didn't contain lead_id, try text parsing
    if (!$lead_id) {
        // Check for "OK, Lead :21046214" format
        if (preg_match('/OK, Lead :(\d+)/', $response, $matches)) {
            $lead_id = $matches[1];
        }
        // Check for other possible text formats
        elseif (preg_match('/Lead(?: ID)?[: ]*(\d+)/i', $response, $matches)) {
            $lead_id = $matches[1];
        }
    }
    
    // If we still don't have an ID, check if API succeeded
    if (!$lead_id) {
        if (strpos($response, 'OK, Lead') !== false) {
            // API succeeded but no lead ID provided - generate our own
            $lead_id = date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
            echo "<p style='color: green;'>✅ <strong>Generated Lead ID:</strong> $lead_id (API succeeded)</p>";
        } else {
            $lead_id = 'TEMP-' . uniqid();
            echo "<p style='color: orange;'>⚠️ <strong>Temporary ID:</strong> $lead_id (API failed/unknown)</p>";
        }
    } else {
        echo "<p style='color: blue;'>🎯 <strong>Extracted Lead ID:</strong> $lead_id</p>";
    }
    
    echo "</div>";
}

echo "<hr>";

echo "<h2>🚀 Test Real Lead Submission:</h2>";

if (isset($_POST['test_submission'])) {
    // Set up test session data
    $_SESSION['origin_city'] = 'Tbilisi';
    $_SESSION['origin_state'] = 'GA';
    $_SESSION['origin_postal_code'] = '0101';
    $_SESSION['destination_city'] = 'Batumi';
    $_SESSION['destination_state'] = 'AD';
    $_SESSION['destination_postal_code'] = '6000';
    $_SESSION['vehicle_year'] = '2020';
    $_SESSION['vehicle_brand'] = 'Toyota';
    $_SESSION['vehicle_model'] = 'Camry';
    $_SESSION['transport_type'] = 1;
    $_SESSION['vehicle_inop'] = 0;
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h3>✅ Test Session Data Set</h3>";
    echo "<p>You can now test the lead submission process:</p>";
    echo "<p><a href='send-leads.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Test Lead Submission</a></p>";
    echo "</div>";
}

if (!isset($_POST['test_submission'])) {
    echo "<form method='post'>";
    echo "<button type='submit' name='test_submission' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
    echo "🧪 Set Up Test Data & Test Submission";
    echo "</button>";
    echo "</form>";
}

echo "<hr>";

echo "<h2>📊 Current Debug Status:</h2>";
echo "<p><a href='test_debug.php'>📋 View All Debug Logs</a></p>";
echo "<p><a href='checkout.php?debug=1'>🛒 Test Checkout (Debug Mode)</a></p>";

echo "<h2>💡 New Lead ID Format Examples:</h2>";
echo "<ul>";
echo "<li><strong>Car:</strong> " . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6)) . " (e.g., 20250718-A1B2C3)</li>";
echo "<li><strong>Boat:</strong> " . date('Ymd') . '-BOAT-' . strtoupper(substr(uniqid(), -6)) . " (e.g., 20250718-BOAT-D4E5F6)</li>";
echo "<li><strong>Heavy:</strong> " . date('Ymd') . '-HEAVY-' . strtoupper(substr(uniqid(), -6)) . " (e.g., 20250718-HEAVY-G7H8I9)</li>";
echo "<li><strong>Motorcycle:</strong> " . date('Ymd') . '-MOTO-' . strtoupper(substr(uniqid(), -6)) . " (e.g., 20250718-MOTO-J1K2L3)</li>";
echo "<li><strong>RV:</strong> " . date('Ymd') . '-RV-' . strtoupper(substr(uniqid(), -6)) . " (e.g., 20250718-RV-M4N5O6)</li>";
echo "</ul>";

echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
