<?php
session_start();

// Simulate a form submission to test the lead ID extraction
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set some test session data
    $_SESSION['origin_city'] = 'New York';
    $_SESSION['origin_state'] = 'NY';
    $_SESSION['origin_postal_code'] = '10001';
    $_SESSION['destination_city'] = 'Los Angeles';
    $_SESSION['destination_state'] = 'CA';
    $_SESSION['destination_postal_code'] = '90001';
    $_SESSION['vehicle_year'] = '2020';
    $_SESSION['vehicle_brand'] = 'Toyota';
    $_SESSION['vehicle_model'] = 'Camry';
    $_SESSION['transport_type'] = 1;
    $_SESSION['vehicle_inop'] = 0;
    
    // Redirect to send-leads.php to test the process
    header("Location: send-leads.php");
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Lead Submission</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { padding: 8px; width: 300px; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        .debug { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Test Lead Submission</h1>
    
    <div class="debug">
        <h3>Current Session Data:</h3>
        <pre><?php print_r($_SESSION); ?></pre>
    </div>
    
    <form method="POST">
        <div class="form-group">
            <label>Name:</label>
            <input type="text" name="name" value="Test User" required>
        </div>
        
        <div class="form-group">
            <label>Email:</label>
            <input type="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label>Phone:</label>
            <input type="tel" name="phone" value="1234567890" required>
        </div>
        
        <div class="form-group">
            <label>Transport From:</label>
            <input type="text" name="transport_from" value="New York, NY 10001" required>
        </div>
        
        <div class="form-group">
            <label>Transport To:</label>
            <input type="text" name="transport_to" value="Los Angeles, CA 90001" required>
        </div>
        
        <div class="form-group">
            <label>Vehicle Brand:</label>
            <input type="text" name="vehicle_brand" value="Toyota" required>
        </div>
        
        <div class="form-group">
            <label>Vehicle Model:</label>
            <input type="text" name="vehicle_model" value="Camry" required>
        </div>
        
        <div class="form-group">
            <label>Vehicle Year:</label>
            <input type="number" name="vehicle_year" value="2020" required>
        </div>
        
        <div class="form-group">
            <label>Transport Type:</label>
            <select name="transport_type">
                <option value="Open">Open</option>
                <option value="Enclosed">Enclosed</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Vehicle Operable:</label>
            <select name="vehicle_operable">
                <option value="yes">Yes</option>
                <option value="no">No</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Available Date:</label>
            <input type="text" name="available_date" value="<?php echo date('m/d/Y'); ?>" required>
        </div>
        
        <button type="submit">🚀 Test Lead Submission</button>
    </form>
    
    <hr>
    <p><a href="test_debug.php">📊 View Debug Information</a></p>
    <p><a href="checkout.php?debug=1">🛒 View Checkout (Debug Mode)</a></p>
</body>
</html>
