<?php
echo "<h1>🔴 LIVE API TEST - BATS CRM</h1>";
echo "<p><strong>⚠️ Warning:</strong> This will make a real API call to BATS CRM</p>";

if (isset($_POST['test_api'])) {
    $authKey = "a68bd6e104f7497ea70b72c29472053d"; // Production key
    
    $testData = [
        "AuthKey" => $authKey,
        "first_name" => "API",
        "last_name" => "Test",
        "email" => "<EMAIL>",
        "phone" => "1234567890",
        "comment_from_shipper" => "API Test Lead",
        "origin_city" => "New York",
        "origin_state" => "NY",
        "origin_postal_code" => "10001",
        "origin_country" => "US",
        "destination_city" => "Los Angeles", 
        "destination_state" => "CA",
        "destination_postal_code" => "90001",
        "destination_country" => "US",
        "ship_date" => date('m/d/Y'),
        "transport_type" => 1,
        "Vehicles" => [[
            "vehicle_inop" => 0,
            "vehicle_make" => "Toyota",
            "vehicle_model" => "Camry",
            "vehicle_model_year" => 2020,
            "vehicle_type" => "Car"
        ]]
    ];
    
    echo "<h2>📤 Sending Request:</h2>";
    echo "<pre style='background: #f0f8ff; padding: 10px; border-radius: 5px; font-size: 12px;'>";
    echo json_encode($testData, JSON_PRETTY_PRINT);
    echo "</pre>";
    
    // Make API call
    $ch = curl_init("https://api.batscrm.com/leads");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($testData))
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "<h2>📥 API Response:</h2>";
    echo "<div style='background: #fff; border: 2px solid #007cba; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>HTTP Code:</strong> <span style='color: " . ($httpCode == 200 ? 'green' : 'red') . ";'>$httpCode</span></p>";
    
    if ($curlError) {
        echo "<p><strong>cURL Error:</strong> <span style='color: red;'>$curlError</span></p>";
    }
    
    echo "<p><strong>Raw Response:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; border: 1px solid #dee2e6; white-space: pre-wrap;'>";
    echo htmlspecialchars($result);
    echo "</pre>";
    echo "</div>";
    
    // Test lead ID extraction
    echo "<h2>🔍 Lead ID Extraction Test:</h2>";
    $lead_id = null;
    
    // Method 1: JSON decode
    echo "<h3>Method 1: JSON Decode</h3>";
    $responseData = json_decode($result, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p>✅ Valid JSON response</p>";
        echo "<pre style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
        print_r($responseData);
        echo "</pre>";
        
        // Check all possible JSON fields
        $possibleFields = ['lead_id', 'leadId', 'id', 'ID', 'lead_ID', 'data.lead_id', 'response.lead_id'];
        foreach ($possibleFields as $field) {
            if (strpos($field, '.') !== false) {
                $parts = explode('.', $field);
                $value = $responseData;
                foreach ($parts as $part) {
                    $value = $value[$part] ?? null;
                    if ($value === null) break;
                }
            } else {
                $value = $responseData[$field] ?? null;
            }
            
            if ($value) {
                echo "<p>✅ Found in field '$field': <strong>$value</strong></p>";
                $lead_id = $value;
                break;
            }
        }
    } else {
        echo "<p>❌ Not valid JSON: " . json_last_error_msg() . "</p>";
    }
    
    // Method 2: Text parsing
    echo "<h3>Method 2: Text Pattern Matching</h3>";
    $patterns = [
        '/OK, Lead :(\d+)/' => 'Old format: OK, Lead :123456',
        '/Lead[: ]+(\d+)/' => 'Lead followed by number',
        '/ID[: ]+(\d+)/' => 'ID followed by number',
        '/(\d{6,})/' => 'Any 6+ digit number',
        '/([A-Z0-9]{8,})/' => 'Any 8+ character alphanumeric'
    ];
    
    foreach ($patterns as $pattern => $description) {
        if (preg_match($pattern, $result, $matches)) {
            echo "<p>✅ Pattern '$description' matched: <strong>{$matches[1]}</strong></p>";
            if (!$lead_id) $lead_id = $matches[1];
        } else {
            echo "<p>❌ Pattern '$description': no match</p>";
        }
    }
    
    // Final result
    echo "<h2>🎯 Final Result:</h2>";
    if ($lead_id) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
        echo "<h3>✅ SUCCESS: Lead ID Found</h3>";
        echo "<p><strong>Lead ID:</strong> $lead_id</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
        echo "<h3>❌ NO LEAD ID FOUND</h3>";
        echo "<p>API returned success but no extractable lead ID.</p>";
        echo "<p><strong>Recommendation:</strong> Generate our own lead ID for tracking.</p>";
        echo "</div>";
    }
    
    // Log this test
    file_put_contents('api_test_log.txt', 
        date('Y-m-d H:i:s') . " - API Test\n" .
        "HTTP Code: $httpCode\n" .
        "Response: $result\n" .
        "Extracted Lead ID: " . ($lead_id ?? 'None') . "\n" .
        "===================\n\n",
        FILE_APPEND
    );
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Live API Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1000px; margin: 0 auto; }
        .warning { background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7; margin: 20px 0; }
        button { padding: 15px 30px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #c82333; }
    </style>
</head>
<body>
    <?php if (!isset($_POST['test_api'])): ?>
    <div class="warning">
        <h3>⚠️ Warning</h3>
        <p>This will make a real API call to BATS CRM with test data. This may create a real lead in their system.</p>
        <p>Only proceed if you want to test the current API response format.</p>
    </div>
    
    <form method="post">
        <button type="submit" name="test_api" onclick="return confirm('Are you sure you want to make a live API call?')">
            🚀 Make Live API Test Call
        </button>
    </form>
    <?php endif; ?>
    
    <hr>
    <p><a href="check_current_api.php">📋 Check Current API Analysis</a></p>
    <p><a href="test_debug.php">📊 View All Debug Info</a></p>
</body>
</html>
