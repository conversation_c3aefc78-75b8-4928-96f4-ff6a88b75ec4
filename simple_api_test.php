<?php
// Simple API test to see what BATS CRM returns

$authKey = "a68bd6e104f7497ea70b72c29472053d";

$testData = [
    "AuthKey" => $authKey,
    "first_name" => "Test",
    "last_name" => "User", 
    "email" => "<EMAIL>",
    "phone" => "1234567890",
    "comment_from_shipper" => "Test lead",
    "origin_city" => "New York",
    "origin_state" => "NY",
    "origin_postal_code" => "10001",
    "origin_country" => "US",
    "destination_city" => "Los Angeles", 
    "destination_state" => "CA",
    "destination_postal_code" => "90001",
    "destination_country" => "US",
    "ship_date" => date('m/d/Y'),
    "transport_type" => 1,
    "Vehicles" => [[
        "vehicle_inop" => 0,
        "vehicle_make" => "Toyota",
        "vehicle_model" => "Camry", 
        "vehicle_model_year" => 2020,
        "vehicle_type" => "Car"
    ]]
];

echo "=== BATS CRM API TEST ===\n";
echo "Request Data: " . json_encode($testData) . "\n\n";

$ch = curl_init("https://api.batscrm.com/leads");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($testData))
]);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Raw Response: $result\n\n";

// Test lead ID extraction
$lead_id = null;

// Try JSON decode first
$responseData = json_decode($result, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "JSON decode successful\n";
    echo "JSON Response: " . print_r($responseData, true) . "\n";
    
    $lead_id = $responseData['lead_id'] ?? 
              $responseData['data']['lead_id'] ?? 
              $responseData['id'] ?? 
              $responseData['leadId'] ?? null;
}

// Try text parsing
if (!$lead_id) {
    echo "Trying text parsing...\n";
    
    if (preg_match('/OK, Lead :(\d+)/', $result, $matches)) {
        $lead_id = $matches[1];
        echo "Found with regex 1: $lead_id\n";
    }
    elseif (preg_match('/Lead[: ]*(\d+)/i', $result, $matches)) {
        $lead_id = $matches[1];
        echo "Found with regex 2: $lead_id\n";
    }
    else {
        echo "No lead ID found\n";
    }
}

if ($lead_id) {
    echo "\n✅ SUCCESS: Lead ID = $lead_id\n";
} else {
    echo "\n❌ FAILED: Could not extract lead ID\n";
}

echo "\n=== END TEST ===\n";
?>
