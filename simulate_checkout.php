<?php
session_start();

// Simulate the data that would be set by send-leads.php
$_SESSION['lead_id'] = 'TEMP-687a31da9da98'; // This is what you're seeing
$_SESSION['api_response_raw'] = 'OK, Lead :21619062'; // This is what should be coming from API
$_SESSION['origin_city'] = 'Tbilisi';
$_SESSION['origin_state'] = 'GA';
$_SESSION['origin_postal_code'] = '0101';
$_SESSION['destination_city'] = 'Batumi';
$_SESSION['destination_state'] = 'AD';
$_SESSION['destination_postal_code'] = '6000';
$_SESSION['vehicle_year'] = '2020';
$_SESSION['vehicle_brand'] = 'Toyota';
$_SESSION['vehicle_model'] = 'Camry';
$_SESSION['transport_type'] = 1;
$_SESSION['vehicle_inop'] = 0;

echo "<h1>🔧 Simulating Checkout Process</h1>";

echo "<h2>Session Data Set:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Testing Lead ID Extraction:</h2>";
$api_response = 'OK, Lead :21619062';
echo "<p><strong>API Response:</strong> " . htmlspecialchars($api_response) . "</p>";

// Test the extraction logic
$lead_id = null;

// First try JSON decode
$responseData = json_decode($api_response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    $lead_id = $responseData['lead_id'] ?? 
              $responseData['data']['lead_id'] ?? 
              $responseData['id'] ?? null;
    echo "<p>JSON decode result: " . ($lead_id ? "Found: $lead_id" : "Not found") . "</p>";
}

// If JSON decode failed or didn't contain lead_id, try text parsing
if (!$lead_id) {
    // Check for "OK, Lead :21046214" format
    if (preg_match('/OK, Lead :(\d+)/', $api_response, $matches)) {
        $lead_id = $matches[1];
        echo "<p>Regex extraction result: Found lead ID: $lead_id</p>";
    }
    // Check for other possible text formats
    elseif (preg_match('/Lead(?: ID)?[: ]*(\d+)/i', $api_response, $matches)) {
        $lead_id = $matches[1];
        echo "<p>Alternative regex extraction result: Found lead ID: $lead_id</p>";
    } else {
        echo "<p>No lead ID found in response</p>";
    }
}

if ($lead_id) {
    echo "<p><strong>✅ Successfully extracted lead ID: $lead_id</strong></p>";
    // Update session with correct lead ID
    $_SESSION['lead_id'] = $lead_id;
} else {
    echo "<p><strong>❌ Failed to extract lead ID</strong></p>";
}

echo "<hr>";
echo "<p><a href='checkout.php?debug=1'>🛒 Go to Checkout (Debug Mode)</a></p>";
echo "<p><a href='test_debug.php'>📊 View Debug Information</a></p>";
?>
