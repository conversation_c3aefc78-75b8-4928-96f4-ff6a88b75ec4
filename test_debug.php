<?php
// Test script to check debug logs and current state

echo "<h1>🔍 Debug Information</h1>";

// Check if debug log files exist
$debugFiles = [
    'checkout_debug.log',
    'lead_extraction_debug.log',
    'error_log.txt',
    'api_response_log.txt'
];

foreach ($debugFiles as $file) {
    echo "<h2>📄 $file</h2>";
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (!empty($content)) {
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
            echo htmlspecialchars($content);
            echo "</pre>";
        } else {
            echo "<p>File exists but is empty.</p>";
        }
    } else {
        echo "<p>File does not exist.</p>";
    }
    echo "<hr>";
}

// Check current session data
session_start();
echo "<h2>🗂️ Current Session Data</h2>";
echo "<pre style='background: #e8f4fd; padding: 10px; border-radius: 5px;'>";
print_r($_SESSION);
echo "</pre>";

// Test API connection
echo "<h2>🌐 Test API Connection</h2>";
$authKey = "a68bd6e104f7497ea70b72c29472053d";
$testData = [
    "AuthKey" => $authKey,
    "first_name" => "Test",
    "last_name" => "User",
    "email" => "<EMAIL>",
    "phone" => "1234567890",
    "comment_from_shipper" => "Test lead",
    "origin_city" => "New York",
    "origin_state" => "NY",
    "origin_postal_code" => "10001",
    "origin_country" => "US",
    "destination_city" => "Los Angeles",
    "destination_state" => "CA",
    "destination_postal_code" => "90001",
    "destination_country" => "US",
    "ship_date" => date('m/d/Y'),
    "transport_type" => 1,
    "Vehicles" => [[
        "vehicle_inop" => 0,
        "vehicle_make" => "Toyota",
        "vehicle_model" => "Camry",
        "vehicle_model_year" => 2020,
        "vehicle_type" => "Car"
    ]]
];

$ch = curl_init("https://api.batscrm.com/leads");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($testData))
]);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>Test API Call Result:</strong></p>";
echo "<p>HTTP Code: $httpCode</p>";
echo "<p>Response: " . htmlspecialchars($result) . "</p>";

// Test lead ID extraction
if ($httpCode === 200) {
    $lead_id = null;
    
    // Try JSON decode
    $responseData = json_decode($result, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        $lead_id = $responseData['lead_id'] ?? 
                  $responseData['data']['lead_id'] ?? 
                  $responseData['id'] ?? null;
    }
    
    // Try text parsing
    if (!$lead_id) {
        if (preg_match('/OK, Lead :(\d+)/', $result, $matches)) {
            $lead_id = $matches[1];
        }
        elseif (preg_match('/Lead(?: ID)?[: ]*(\d+)/i', $result, $matches)) {
            $lead_id = $matches[1];
        }
    }
    
    echo "<p><strong>Extracted Lead ID:</strong> " . ($lead_id ?? 'Could not extract') . "</p>";
}

echo "<hr>";
echo "<p><em>Generated at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
