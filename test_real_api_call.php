<?php
echo "<h1>🔴 REAL BATS CRM API TEST</h1>";
echo "<p><strong>⚠️ Warning:</strong> This will make a real API call to BATS CRM</p>";

if (isset($_POST['test_api'])) {
    $authKey = "a68bd6e104f7497ea70b72c29472053d"; // Production key
    
    echo "<h2>📤 Making Real API Call...</h2>";
    
    // Test data
    $testData = [
        "AuthKey" => $authKey,
        "first_name" => "API",
        "last_name" => "Test",
        "email" => "apitest" . time() . "@example.com", // Unique email
        "phone" => "1234567890",
        "comment_from_shipper" => "API Test Lead - " . date('Y-m-d H:i:s'),
        "origin_city" => "New York",
        "origin_state" => "NY", 
        "origin_postal_code" => "10001",
        "origin_country" => "US",
        "destination_city" => "Los Angeles",
        "destination_state" => "CA",
        "destination_postal_code" => "90001", 
        "destination_country" => "US",
        "ship_date" => date('m/d/Y'),
        "transport_type" => 1,
        "Vehicles" => [[
            "vehicle_inop" => 0,
            "vehicle_make" => "Toyota",
            "vehicle_model" => "Camry", 
            "vehicle_model_year" => 2020,
            "vehicle_type" => "Car"
        ]]
    ];
    
    echo "<h3>Request Data:</h3>";
    echo "<pre style='background: #f0f8ff; padding: 10px; border-radius: 5px; font-size: 12px;'>";
    echo json_encode($testData, JSON_PRETTY_PRINT);
    echo "</pre>";
    
    // Make API call
    $ch = curl_init("https://api.batscrm.com/leads");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($testData))
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    
    $startTime = microtime(true);
    $result = curl_exec($ch);
    $endTime = microtime(true);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    $responseTime = round(($endTime - $startTime) * 1000, 2);
    
    echo "<h2>📥 API Response:</h2>";
    echo "<div style='background: #fff; border: 2px solid " . ($httpCode == 200 ? 'green' : 'red') . "; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>HTTP Code:</strong> <span style='color: " . ($httpCode == 200 ? 'green' : 'red') . "; font-size: 18px; font-weight: bold;'>$httpCode</span></p>";
    echo "<p><strong>Response Time:</strong> {$responseTime}ms</p>";
    
    if ($curlError) {
        echo "<p><strong>cURL Error:</strong> <span style='color: red;'>$curlError</span></p>";
    }
    
    echo "<p><strong>Raw Response:</strong></p>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars($result);
    echo "</div>";
    echo "</div>";
    
    // Test lead ID extraction with current logic
    echo "<h2>🔍 Lead ID Extraction Test:</h2>";
    $lead_id = null;
    
    echo "<h3>Method 1: JSON Decode</h3>";
    $responseData = json_decode($result, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p>✅ Valid JSON response</p>";
        echo "<pre style='background: #e8f5e8; padding: 10px; border-radius: 5px; font-size: 12px;'>";
        print_r($responseData);
        echo "</pre>";
        
        $lead_id = $responseData['lead_id'] ?? 
                  $responseData['data']['lead_id'] ?? 
                  $responseData['id'] ?? null;
        
        if ($lead_id) {
            echo "<p style='color: green; font-weight: bold;'>✅ Found lead ID in JSON: $lead_id</p>";
        } else {
            echo "<p style='color: orange;'>❌ No lead ID found in JSON</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Not valid JSON: " . json_last_error_msg() . "</p>";
    }
    
    echo "<h3>Method 2: Text Pattern Matching</h3>";
    if (!$lead_id) {
        // Check for "OK, Lead :21046214" format (old format)
        if (preg_match('/OK, Lead :(\d+)/', $result, $matches)) {
            $lead_id = $matches[1];
            echo "<p style='color: green; font-weight: bold;'>✅ Found with OLD format regex: $lead_id</p>";
        }
        // Check for other possible text formats
        elseif (preg_match('/Lead(?: ID)?[: ]*(\d+)/i', $result, $matches)) {
            $lead_id = $matches[1];
            echo "<p style='color: green; font-weight: bold;'>✅ Found with alternative regex: $lead_id</p>";
        }
        else {
            echo "<p style='color: orange;'>❌ No lead ID found with text parsing</p>";
        }
    }
    
    echo "<h3>Method 3: New Logic (Generate if Success)</h3>";
    if (!$lead_id) {
        if (strpos($result, 'OK, Lead') !== false) {
            $generated_id = date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
            echo "<p style='color: blue; font-weight: bold;'>🔄 API succeeded but no lead ID provided. Generated: $generated_id</p>";
            $lead_id = $generated_id;
        } else {
            $temp_id = 'TEMP-' . uniqid();
            echo "<p style='color: red;'>❌ API failed or unknown response. Using temp ID: $temp_id</p>";
            $lead_id = $temp_id;
        }
    }
    
    // Final result
    echo "<h2>🎯 Final Result:</h2>";
    echo "<div style='background: " . ($lead_id && !strpos($lead_id, 'TEMP') ? '#d4edda' : '#fff3cd') . "; padding: 20px; border-radius: 10px; border: 2px solid " . ($lead_id && !strpos($lead_id, 'TEMP') ? '#c3e6cb' : '#ffeaa7') . ";'>";
    echo "<h3 style='margin-top: 0;'>" . ($lead_id && !strpos($lead_id, 'TEMP') ? '✅ SUCCESS' : '⚠️ FALLBACK') . "</h3>";
    echo "<p><strong>Final Lead ID:</strong> <span style='font-size: 24px; font-weight: bold; color: #007cba;'>$lead_id</span></p>";
    
    if (strpos($result, 'OK, Lead :') !== false && preg_match('/OK, Lead :(\d+)/', $result)) {
        echo "<p style='color: green;'>🎉 <strong>GREAT NEWS!</strong> BATS CRM still returns lead IDs in the old format!</p>";
    } elseif (strpos($result, 'OK, Lead') !== false) {
        echo "<p style='color: orange;'>📝 BATS CRM confirms lead creation but doesn't provide lead ID anymore.</p>";
    } else {
        echo "<p style='color: red;'>❌ API call failed or returned unexpected response.</p>";
    }
    echo "</div>";
    
    // Log this test
    file_put_contents('real_api_test_log.txt', 
        "=== REAL API TEST " . date('Y-m-d H:i:s') . " ===\n" .
        "HTTP Code: $httpCode\n" .
        "Response Time: {$responseTime}ms\n" .
        "Raw Response: $result\n" .
        "Extracted Lead ID: $lead_id\n" .
        "===========================================\n\n",
        FILE_APPEND
    );
    
    echo "<hr>";
    echo "<p><strong>📝 Test logged to:</strong> real_api_test_log.txt</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Real BATS CRM API Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .warning { background: #fff3cd; padding: 20px; border-radius: 10px; border: 2px solid #ffeaa7; margin: 20px 0; }
        .danger { background: #f8d7da; padding: 20px; border-radius: 10px; border: 2px solid #f5c6cb; margin: 20px 0; }
        button { padding: 15px 30px; background: #dc3545; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; }
        button:hover { background: #c82333; }
    </style>
</head>
<body>
    <?php if (!isset($_POST['test_api'])): ?>
    <div class="danger">
        <h3>⚠️ IMPORTANT WARNING</h3>
        <p><strong>This will make a REAL API call to BATS CRM!</strong></p>
        <ul>
            <li>This will create a real lead in BATS CRM system</li>
            <li>This will use your production API key</li>
            <li>This may count against your API limits</li>
            <li>Only proceed if you want to test the current API behavior</li>
        </ul>
    </div>
    
    <div class="warning">
        <h3>🎯 Purpose of This Test</h3>
        <p>This test will help us determine:</p>
        <ul>
            <li>What format BATS CRM API currently returns</li>
            <li>Whether lead IDs are still provided in responses</li>
            <li>If our extraction logic works correctly</li>
        </ul>
    </div>
    
    <form method="post">
        <button type="submit" name="test_api" onclick="return confirm('⚠️ WARNING: This will make a REAL API call to BATS CRM and create a real lead. Are you absolutely sure you want to proceed?')">
            🚀 Make REAL API Call to BATS CRM
        </button>
    </form>
    
    <hr>
    <h3>📋 Alternative Options:</h3>
    <p><a href="test_new_lead_logic.php">🧪 Test Lead Logic (Simulation Only)</a></p>
    <p><a href="test_debug.php">📊 View Debug Information</a></p>
    <p><a href="checkout.php?debug=1">🛒 Test Checkout (Debug Mode)</a></p>
    <?php endif; ?>
</body>
</html>
