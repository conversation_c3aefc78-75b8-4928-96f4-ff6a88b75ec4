<?php
// Quick API test to check current BATS CRM response format

echo "🔍 Quick BATS CRM API Test\n";
echo "==========================\n\n";

$authKey = "a68bd6e104f7497ea70b72c29472053d"; // Production key

$testData = [
    "AuthKey" => $authKey,
    "first_name" => "QuickTest",
    "last_name" => "User",
    "email" => "quicktest" . time() . "@example.com",
    "phone" => "1234567890",
    "comment_from_shipper" => "Quick API Test",
    "origin_city" => "New York",
    "origin_state" => "NY",
    "origin_postal_code" => "10001",
    "origin_country" => "US",
    "destination_city" => "Los Angeles",
    "destination_state" => "CA",
    "destination_postal_code" => "90001",
    "destination_country" => "US",
    "ship_date" => date('m/d/Y'),
    "transport_type" => 1,
    "Vehicles" => [[
        "vehicle_inop" => 0,
        "vehicle_make" => "Toyota",
        "vehicle_model" => "Camry",
        "vehicle_model_year" => 2020,
        "vehicle_type" => "Car"
    ]]
];

echo "📤 Sending request to BATS CRM API...\n";

$ch = curl_init("https://api.batscrm.com/leads");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($testData))
]);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "📥 API Response:\n";
echo "HTTP Code: $httpCode\n";
if ($curlError) {
    echo "cURL Error: $curlError\n";
}
echo "Raw Response: $result\n\n";

// Test lead ID extraction
echo "🔍 Testing Lead ID Extraction:\n";
$lead_id = null;

// Method 1: JSON decode
$responseData = json_decode($result, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Valid JSON response\n";
    $lead_id = $responseData['lead_id'] ?? 
              $responseData['data']['lead_id'] ?? 
              $responseData['id'] ?? null;
    if ($lead_id) {
        echo "✅ Found lead ID in JSON: $lead_id\n";
    }
} else {
    echo "❌ Not valid JSON\n";
}

// Method 2: Text parsing
if (!$lead_id) {
    if (preg_match('/OK, Lead :(\d+)/', $result, $matches)) {
        $lead_id = $matches[1];
        echo "✅ Found with OLD format regex: $lead_id\n";
    } elseif (preg_match('/Lead(?: ID)?[: ]*(\d+)/i', $result, $matches)) {
        $lead_id = $matches[1];
        echo "✅ Found with alternative regex: $lead_id\n";
    }
}

// Method 3: Generate if success
if (!$lead_id) {
    if (strpos($result, 'OK, Lead') !== false) {
        $lead_id = date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
        echo "🔄 API succeeded but no lead ID. Generated: $lead_id\n";
    } else {
        $lead_id = 'TEMP-' . uniqid();
        echo "❌ API failed. Using temp ID: $lead_id\n";
    }
}

echo "\n🎯 Final Lead ID: $lead_id\n";

// Log the result
file_put_contents('quick_api_test_result.txt', 
    date('Y-m-d H:i:s') . " - HTTP: $httpCode - Response: $result - Lead ID: $lead_id\n",
    FILE_APPEND
);

echo "\n📝 Result logged to quick_api_test_result.txt\n";
?>
