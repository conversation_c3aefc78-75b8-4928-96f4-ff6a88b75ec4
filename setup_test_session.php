<?php
session_start();

// Set up test session data for checkout
$_SESSION['lead_id'] = '21619062'; // Real lead ID format
$_SESSION['api_response_raw'] = 'OK, Lead :21619062';
$_SESSION['origin_city'] = 'Tbilisi';
$_SESSION['origin_state'] = 'GA';
$_SESSION['origin_postal_code'] = '0101';
$_SESSION['destination_city'] = 'Batumi';
$_SESSION['destination_state'] = 'AD';
$_SESSION['destination_postal_code'] = '6000';
$_SESSION['vehicle_year'] = '2020';
$_SESSION['vehicle_brand'] = 'Toyota';
$_SESSION['vehicle_model'] = 'Camry';
$_SESSION['transport_type'] = 1;
$_SESSION['vehicle_inop'] = 0;

echo "<h1>✅ Test Session Data Set Up</h1>";
echo "<p>Session data has been configured for testing checkout page.</p>";

echo "<h2>Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<hr>";
echo "<p><a href='checkout.php?debug=1'>🛒 Go to Checkout (Debug Mode)</a></p>";
echo "<p><a href='checkout.php'>🛒 Go to Checkout (Normal Mode)</a></p>";

// Also test with TEMP ID to see the difference
echo "<hr>";
echo "<h2>🧪 Test with TEMP ID:</h2>";
echo "<form method='post'>";
echo "<button type='submit' name='set_temp'>Set TEMP Lead ID</button>";
echo "</form>";

if (isset($_POST['set_temp'])) {
    $_SESSION['lead_id'] = 'TEMP-687a31da9da98';
    $_SESSION['api_response_raw'] = 'Some error response';
    echo "<p>✅ TEMP Lead ID set. <a href='checkout.php?debug=1'>View Checkout</a></p>";
}
?>
